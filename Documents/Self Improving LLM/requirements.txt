# Darwin Gödel Machine Requirements
# Core dependencies for the self-improving agent system

# Ollama client for Qwen3 model interaction
ollama>=0.1.0

# Web framework for potential API endpoints
fastapi>=0.100.0
uvicorn>=0.20.0

# Database and data handling
sqlalchemy>=2.0.0
# sqlite3 is built-in with Python

# Testing framework
pytest>=7.0.0
pytest-cov>=4.0.0

# Data processing
numpy>=1.24.0
pandas>=2.0.0

# Utilities
pathlib  # Built-in with Python
json  # Built-in with Python
hashlib  # Built-in with Python
subprocess  # Built-in with Python
logging  # Built-in with Python

# Optional: For enhanced functionality
docker>=6.0.0  # For advanced sandboxing
psutil>=5.9.0  # For system monitoring
requests>=2.28.0  # For HTTP requests

# Development tools
black>=23.0.0  # Code formatting
flake8>=6.0.0  # Linting
mypy>=1.0.0  # Type checking
