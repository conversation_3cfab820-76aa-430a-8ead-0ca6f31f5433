#!/usr/bin/env python3
"""
Basic functionality test for Darwin Gödel Machine
Tests core components before running the full DGM
"""

import sys
import logging
from pathlib import Path
import tempfile
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ollama_connection():
    """Test connection to Ollama and Qwen model"""
    try:
        import ollama
        
        # Test basic connection
        models = ollama.list()
        logger.info("✓ Ollama connection successful")
        
        # Check for Qwen model
        model_names = [model.model for model in models.models if model.model]
        if any('qwen3' in name.lower() for name in model_names):
            logger.info("✓ Qwen3 model found")
            return True
        else:
            logger.warning("⚠ Qwen3 model not found in available models")
            logger.info(f"Available models: {model_names}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Ollama connection failed: {e}")
        return False

def test_agent_creation():
    """Test basic agent creation and tool functionality"""
    try:
        from dgm_agent import QwenCodingAgent, <PERSON><PERSON><PERSON><PERSON>, EditTool
        
        # Test tool creation
        bash_tool = BashTool()
        edit_tool = EditTool()
        
        logger.info("✓ Tools created successfully")
        
        # Test tool info
        bash_info = bash_tool.tool_info()
        edit_info = edit_tool.tool_info()
        
        assert 'name' in bash_info
        assert 'name' in edit_info
        
        logger.info("✓ Tool info validation passed")
        
        # Test basic tool functionality
        result = bash_tool.tool_function("echo 'Hello World'")
        assert "Hello World" in result
        
        logger.info("✓ Bash tool test passed")
        
        # Test file operations in current directory for safety
        test_file = Path("test_temp_file.txt")
        test_file.write_text("test content")

        try:
            result = edit_tool.tool_function("view", str(test_file))
            assert "test content" in result
            logger.info("✓ Edit tool view test passed")

            result = edit_tool.tool_function("str_replace", str(test_file),
                                           old_str="test", new_str="modified")
            assert "Successfully replaced" in result
            logger.info("✓ Edit tool str_replace test passed")

        finally:
            if test_file.exists():
                test_file.unlink()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Agent creation test failed: {e}")
        return False

def test_archive_system():
    """Test the archive management system"""
    try:
        from dgm_agent import ArchiveManager, AgentPerformance
        from datetime import datetime
        
        # Create temporary archive
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_db = f.name
        
        try:
            archive = ArchiveManager(temp_db)
            
            # Test adding an agent
            test_code = "print('hello world')"
            performance = AgentPerformance(
                agent_id="test",
                benchmark_score=0.5,
                tasks_completed=1,
                tasks_attempted=2,
                execution_time=10.0,
                timestamp=datetime.now().isoformat()
            )
            
            agent_id = archive.add_agent(test_code, performance)
            logger.info(f"✓ Agent added to archive: {agent_id}")
            
            # Test retrieving agent
            retrieved = archive.get_agent(agent_id)
            assert retrieved is not None
            assert retrieved['performance_score'] == 0.5
            
            logger.info("✓ Agent retrieval test passed")
            
            # Test parent selection
            parents = archive.select_parents(1)
            assert len(parents) == 1
            assert parents[0] == agent_id
            
            logger.info("✓ Parent selection test passed")
            
            return True
            
        finally:
            Path(temp_db).unlink()
            
    except Exception as e:
        logger.error(f"✗ Archive system test failed: {e}")
        return False

def test_benchmark_tasks():
    """Test the benchmark task system"""
    try:
        from dgm_engine import BenchmarkSuite, BenchmarkTask
        
        suite = BenchmarkSuite()
        
        # Test task creation
        assert len(suite.tasks) > 0
        logger.info(f"✓ Benchmark suite created with {len(suite.tasks)} tasks")
        
        # Test task retrieval
        task = suite.get_task("simple_function")
        assert task is not None
        assert task.task_id == "simple_function"
        
        logger.info("✓ Task retrieval test passed")
        
        # Test random task selection
        random_tasks = suite.get_random_tasks(2)
        assert len(random_tasks) <= 2
        
        logger.info("✓ Random task selection test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Benchmark task test failed: {e}")
        return False

def test_simple_model_interaction():
    """Test basic interaction with the Qwen model"""
    try:
        from dgm_agent import QwenCodingAgent
        
        # Note: This test requires the model to be available
        # Skip if model is not available
        if not test_ollama_connection():
            logger.warning("⚠ Skipping model interaction test - Ollama/Qwen not available")
            return True
        
        agent = QwenCodingAgent()
        
        # Test simple chat
        response = agent.chat_with_model("What is 2 + 2?", "You are a helpful assistant.")
        
        assert len(response) > 0
        logger.info("✓ Basic model interaction test passed")
        
        # Test tool execution
        result = agent.execute_tool("bash", command="echo 'test'")
        assert "test" in result
        
        logger.info("✓ Tool execution test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Model interaction test failed: {e}")
        return False

def run_all_tests():
    """Run all basic functionality tests"""
    
    print("Darwin Gödel Machine - Basic Functionality Tests")
    print("=" * 50)
    
    tests = [
        ("Ollama Connection", test_ollama_connection),
        ("Agent Creation", test_agent_creation),
        ("Archive System", test_archive_system),
        ("Benchmark Tasks", test_benchmark_tasks),
        ("Model Interaction", test_simple_model_interaction)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! DGM is ready to run.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please resolve issues before running DGM.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
