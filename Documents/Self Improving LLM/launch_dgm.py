#!/usr/bin/env python3
"""
Darwin Gödel Machine Launcher
Easy-to-use launcher script for the self-improving agent system
"""

import sys
import os
import argparse
import logging
from pathlib import Path

def setup_logging(verbose=False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dgm_launcher.log'),
            logging.StreamHandler()
        ]
    )

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("Checking prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9+ required")
        return False
    print("✅ Python version OK")
    
    # Check if files exist
    required_files = [
        'dgm_agent.py',
        'dgm_engine.py', 
        'requirements.txt',
        'setup.py'
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ Missing required file: {file}")
            return False
    print("✅ Required files present")
    
    # Check Ollama
    try:
        import ollama
        models = ollama.list()
        print("✅ Ollama connection OK")
        
        # Check for Qwen model
        model_names = [model.model for model in models.models if model.model]
        if any('qwen3' in name.lower() for name in model_names):
            print("✅ Qwen3 model available")
        else:
            print("⚠️  Qwen3 model not found - will try to continue")
            
    except Exception as e:
        print(f"❌ Ollama check failed: {e}")
        return False
    
    return True

def run_setup():
    """Run the setup process"""
    print("\n🔧 Running setup...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'setup.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Setup completed successfully")
            return True
        else:
            print(f"❌ Setup failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False

def run_tests():
    """Run basic functionality tests"""
    print("\n🧪 Running tests...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_basic_functionality.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print(f"❌ Tests failed: {result.stderr}")
            print("Output:", result.stdout)
            return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def run_dgm(iterations=None, tasks=None, model=None):
    """Run the Darwin Gödel Machine"""
    print("\n🚀 Starting Darwin Gödel Machine...")
    
    try:
        from dgm_engine import DGMEngine
        
        # Create engine with custom model if specified
        if model:
            engine = DGMEngine(model_name=model)
        else:
            engine = DGMEngine()
        
        # Customize parameters if specified
        if iterations:
            engine.max_iterations = iterations
        
        if tasks:
            # This would require modifying the engine to accept custom task count
            print(f"Note: Custom task count ({tasks}) not yet implemented")
        
        # Run the engine
        engine.run()
        
        print("✅ DGM completed successfully")
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️  DGM stopped by user")
        return True
    except Exception as e:
        print(f"❌ DGM error: {e}")
        return False

def interactive_mode():
    """Run in interactive mode with user prompts"""
    print("\n🎯 Darwin Gödel Machine - Interactive Mode")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Run setup")
        print("2. Run tests") 
        print("3. Start DGM")
        print("4. Check status")
        print("5. View logs")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == '1':
            run_setup()
        elif choice == '2':
            run_tests()
        elif choice == '3':
            # Get parameters
            iterations = input("Max iterations (default 80): ").strip()
            iterations = int(iterations) if iterations else None
            
            model = input("Model name (default qwen3:30b-a3b): ").strip()
            model = model if model else None
            
            run_dgm(iterations=iterations, model=model)
        elif choice == '4':
            check_prerequisites()
        elif choice == '5':
            # Show recent logs
            log_files = ['dgm.log', 'dgm_launcher.log']
            for log_file in log_files:
                if Path(log_file).exists():
                    print(f"\n--- {log_file} (last 20 lines) ---")
                    with open(log_file, 'r') as f:
                        lines = f.readlines()
                        for line in lines[-20:]:
                            print(line.rstrip())
        elif choice == '6':
            print("Goodbye! 👋")
            break
        else:
            print("Invalid choice. Please select 1-6.")

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(
        description="Darwin Gödel Machine Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_dgm.py                    # Interactive mode
  python launch_dgm.py --setup           # Run setup only
  python launch_dgm.py --test            # Run tests only  
  python launch_dgm.py --run             # Run DGM directly
  python launch_dgm.py --run --iterations 50  # Run with custom iterations
        """
    )
    
    parser.add_argument('--setup', action='store_true', 
                       help='Run setup process')
    parser.add_argument('--test', action='store_true',
                       help='Run functionality tests')
    parser.add_argument('--run', action='store_true',
                       help='Run DGM directly')
    parser.add_argument('--interactive', action='store_true',
                       help='Run in interactive mode (default)')
    parser.add_argument('--iterations', type=int, default=None,
                       help='Maximum number of DGM iterations')
    parser.add_argument('--tasks', type=int, default=None,
                       help='Number of tasks per evaluation')
    parser.add_argument('--model', type=str, default=None,
                       help='Model name to use (default: qwen3:30b-a3b)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    print("🧬 Darwin Gödel Machine Launcher")
    print("Self-Improving AI for Qwen3:30b-a3b")
    print("=" * 40)
    
    # Check prerequisites first
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please resolve issues and try again.")
        return 1
    
    # Execute based on arguments
    if args.setup:
        success = run_setup()
        return 0 if success else 1
    
    elif args.test:
        success = run_tests()
        return 0 if success else 1
    
    elif args.run:
        success = run_dgm(
            iterations=args.iterations,
            tasks=args.tasks,
            model=args.model
        )
        return 0 if success else 1
    
    else:
        # Default to interactive mode
        interactive_mode()
        return 0

if __name__ == "__main__":
    sys.exit(main())
