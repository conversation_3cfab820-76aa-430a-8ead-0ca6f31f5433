#!/usr/bin/env python3
"""
Fast Darwin Gödel Machine Runner
Optimized for speed with checkpoint support
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from dgm_engine import DGMEngine

class FastDGMRunner:
    """Fast DGM runner with checkpoint support"""
    
    def __init__(self, max_iterations: int = 25, checkpoint_file: str = "dgm_checkpoint.json"):
        self.max_iterations = max_iterations
        self.checkpoint_file = checkpoint_file
        self.engine = None
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        self.running = False
        if self.engine:
            self.engine.save_checkpoint()
            print("✅ Checkpoint saved")
    
    def run(self):
        """Run the DGM with optimizations"""
        
        print("🚀 Fast Darwin Gödel Machine Runner")
        print("=" * 50)
        
        # Setup logging for performance
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dgm_fast.log'),
                logging.StreamHandler()
            ]
        )
        
        try:
            # Initialize engine with checkpoint support
            self.engine = DGMEngine(
                model_name="qwen3:30b-a3b",
                max_iterations=self.max_iterations,
                checkpoint_file=self.checkpoint_file
            )
            
            print(f"📊 Configuration:")
            print(f"   Max iterations: {self.max_iterations}")
            print(f"   Checkpoint file: {self.checkpoint_file}")
            print(f"   Starting from iteration: {self.engine.iteration_count}")
            print(f"   Current best score: {self.engine.best_score:.2f}")
            
            if self.engine.iteration_count > 0:
                print(f"🔄 Resuming from checkpoint...")
            else:
                print(f"🆕 Starting fresh run...")
                # Initial evaluation
                initial_code = Path("dgm_agent.py").read_text()
                initial_performance = self.engine.evaluate_agent(initial_code, num_tasks=3)
                print(f"📈 Initial performance: {initial_performance.benchmark_score:.2f}")
            
            print(f"⏱️  Estimated time: {(self.max_iterations - self.engine.iteration_count) * 6:.0f} minutes")
            print("🔄 Starting DGM iterations...")
            print("   Press Ctrl+C to stop gracefully and save checkpoint")
            print()
            
            # Main DGM loop
            while (self.engine.iteration_count < self.max_iterations and self.running):
                iteration_start = time.time()
                
                try:
                    success = self.engine.run_dgm_iteration()
                    iteration_time = time.time() - iteration_start
                    
                    if success:
                        progress = (self.engine.iteration_count / self.max_iterations) * 100
                        print(f"✅ Iteration {self.engine.iteration_count}/{self.max_iterations} "
                              f"({progress:.1f}%) - {iteration_time:.1f}s - "
                              f"Best: {self.engine.best_score:.2f}")
                    else:
                        print(f"❌ Iteration {self.engine.iteration_count} failed")
                    
                    # Brief pause between iterations
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"💥 Error in iteration {self.engine.iteration_count}: {e}")
                    break
            
            if self.running:
                print(f"\n🎉 DGM completed successfully!")
            else:
                print(f"\n⏹️  DGM stopped by user")
            
            self.engine.print_final_report()
            
        except KeyboardInterrupt:
            print(f"\n⏹️  DGM interrupted by user")
            if self.engine:
                self.engine.save_checkpoint()
                print("✅ Checkpoint saved")
        except Exception as e:
            print(f"💥 DGM error: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Fast Darwin Gödel Machine Runner")
    parser.add_argument("--iterations", "-i", type=int, default=25,
                       help="Maximum number of iterations (default: 25)")
    parser.add_argument("--checkpoint", "-c", type=str, default="dgm_checkpoint.json",
                       help="Checkpoint file path (default: dgm_checkpoint.json)")
    parser.add_argument("--resume", "-r", action="store_true",
                       help="Resume from existing checkpoint")
    parser.add_argument("--reset", action="store_true",
                       help="Reset and start fresh (delete checkpoint)")
    
    args = parser.parse_args()
    
    # Handle reset option
    if args.reset:
        checkpoint_path = Path(args.checkpoint)
        if checkpoint_path.exists():
            checkpoint_path.unlink()
            print(f"🗑️  Deleted checkpoint: {args.checkpoint}")
        else:
            print(f"ℹ️  No checkpoint to delete: {args.checkpoint}")
    
    # Create and run the fast DGM
    runner = FastDGMRunner(
        max_iterations=args.iterations,
        checkpoint_file=args.checkpoint
    )
    
    runner.run()

if __name__ == "__main__":
    main()
