#!/usr/bin/env python3
"""
Darwin Gödel Machine Engine
Main execution engine for the self-improving agent system
"""

import os
import time
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import random
from datetime import datetime

from dgm_agent import QwenCodingAgent, AgentPerformance, SolutionAttempt

logger = logging.getLogger(__name__)

class BenchmarkTask:
    """Represents a coding benchmark task"""
    
    def __init__(self, task_id: str, description: str, repo_path: str,
                 test_command: str, expected_files: Optional[List[str]] = None):
        self.task_id = task_id
        self.description = description
        self.repo_path = repo_path
        self.test_command = test_command
        self.expected_files = expected_files or []

class BenchmarkSuite:
    """Collection of benchmark tasks for evaluation"""
    
    def __init__(self):
        self.tasks = []
        self.setup_basic_tasks()
    
    def setup_basic_tasks(self):
        """Setup basic coding tasks for initial testing"""
        
        # Task 1: Simple function implementation
        task1 = BenchmarkTask(
            task_id="simple_function",
            description="""Create a Python file called 'math_utils.py' with a function called 'fibonacci' that:
1. Takes an integer n as input
2. Returns the nth Fibonacci number
3. Handles edge cases (n <= 0 should return 0, n == 1 should return 1)
4. Include proper docstring and type hints

Also create a test file 'test_math_utils.py' that tests the function with various inputs.""",
            repo_path="/tmp/test_repo_1",
            test_command="cd /tmp/test_repo_1 && python -m pytest test_math_utils.py -v",
            expected_files=["math_utils.py", "test_math_utils.py"]
        )
        
        # Task 2: Class implementation
        task2 = BenchmarkTask(
            task_id="class_implementation",
            description="""Create a Python file called 'data_structures.py' with a Stack class that:
1. Implements push(), pop(), peek(), is_empty(), and size() methods
2. Handles empty stack operations gracefully
3. Uses proper type hints and docstrings
4. Includes a __str__ method for string representation

Create comprehensive tests in 'test_data_structures.py'.""",
            repo_path="/tmp/test_repo_2",
            test_command="cd /tmp/test_repo_2 && python -m pytest test_data_structures.py -v",
            expected_files=["data_structures.py", "test_data_structures.py"]
        )
        
        # Task 3: File processing
        task3 = BenchmarkTask(
            task_id="file_processing",
            description="""Create a Python script 'log_analyzer.py' that:
1. Reads a log file and extracts error messages
2. Counts occurrences of different error types
3. Generates a summary report
4. Handles file not found and permission errors gracefully

Include sample log file and test cases.""",
            repo_path="/tmp/test_repo_3",
            test_command="cd /tmp/test_repo_3 && python -m pytest test_log_analyzer.py -v",
            expected_files=["log_analyzer.py", "test_log_analyzer.py", "sample.log"]
        )
        
        # Task 4: Algorithm implementation
        task4 = BenchmarkTask(
            task_id="algorithm_implementation",
            description="""Create a Python file called 'algorithms.py' with a function called 'binary_search' that:
1. Takes a sorted list and a target value as input
2. Returns the index of the target if found, -1 if not found
3. Uses the binary search algorithm (not linear search)
4. Includes proper type hints and docstring
5. Handles edge cases (empty list, single element, etc.)

Create comprehensive tests in 'test_algorithms.py'.""",
            repo_path="/tmp/test_repo_4",
            test_command="cd /tmp/test_repo_4 && python -m pytest test_algorithms.py -v",
            expected_files=["algorithms.py", "test_algorithms.py"]
        )

        # Task 5: String processing
        task5 = BenchmarkTask(
            task_id="string_processing",
            description="""Create a Python file called 'text_utils.py' with functions:
1. 'word_count(text)' - counts words in text
2. 'reverse_words(text)' - reverses word order in a sentence
3. 'is_palindrome(text)' - checks if text is a palindrome (ignore case/spaces)
4. All functions should have type hints and docstrings

Create tests in 'test_text_utils.py' that verify all functions work correctly.""",
            repo_path="/tmp/test_repo_5",
            test_command="cd /tmp/test_repo_5 && python -m pytest test_text_utils.py -v",
            expected_files=["text_utils.py", "test_text_utils.py"]
        )

        self.tasks = [task1, task2, task3, task4, task5]
    
    def get_task(self, task_id: str) -> Optional[BenchmarkTask]:
        """Get a specific task by ID"""
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None
    
    def get_random_tasks(self, count: int) -> List[BenchmarkTask]:
        """Get random tasks for evaluation"""
        return random.sample(self.tasks, min(count, len(self.tasks)))

class DGMEngine:
    """Main Darwin Gödel Machine execution engine"""
    
    def __init__(self, model_name: str = "qwen3:30b-a3b", max_iterations: int = 25):
        self.agent = QwenCodingAgent(model_name)
        self.benchmark = BenchmarkSuite()
        self.iteration_count = 0
        self.max_iterations = max_iterations
        self.evaluation_logs = []
        self.best_score = 0.0
        self.improvement_history = []
        
        # Create initial agent entry in archive
        initial_code = Path("dgm_agent.py").read_text()
        initial_performance = AgentPerformance(
            agent_id="initial",
            benchmark_score=0.0,
            tasks_completed=0,
            tasks_attempted=0,
            execution_time=0.0,
            timestamp=datetime.now().isoformat()
        )
        self.agent.archive.add_agent(initial_code, initial_performance)
        
        logger.info("DGM Engine initialized")
    
    def evaluate_agent(self, agent_code: str, num_tasks: int = 3) -> AgentPerformance:
        """Evaluate an agent on a set of benchmark tasks"""
        
        logger.info(f"Evaluating agent on {num_tasks} tasks")
        
        # Save current agent code temporarily
        temp_agent_file = Path("temp_agent.py")
        temp_agent_file.write_text(agent_code)
        
        tasks = self.benchmark.get_random_tasks(num_tasks)
        completed_tasks = 0
        total_time = 0.0
        task_logs = []
        
        for task in tasks:
            logger.info(f"Running task: {task.task_id}")
            
            # Setup task environment
            task_path = Path(task.repo_path)
            task_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            try:
                # Execute the task
                solution, success = self.agent.solve_coding_task(
                    task.description, 
                    task.repo_path
                )
                
                execution_time = time.time() - start_time
                total_time += execution_time

                # Verify solution by running tests
                test_result = ""
                if success:
                    test_result = self.agent.execute_tool("bash", command=task.test_command)
                    if "FAILED" not in test_result and "ERROR" not in test_result:
                        completed_tasks += 1
                        logger.info(f"Task {task.task_id} completed successfully")
                    else:
                        logger.info(f"Task {task.task_id} failed tests")
                        success = False
                else:
                    logger.info(f"Task {task.task_id} failed to generate solution")

                task_logs.append({
                    "task_id": task.task_id,
                    "success": success,
                    "execution_time": execution_time,
                    "solution": solution[:500],  # Truncate for storage
                    "test_result": test_result[:500] if test_result else ""
                })
                
            except Exception as e:
                logger.error(f"Error executing task {task.task_id}: {e}")
                task_logs.append({
                    "task_id": task.task_id,
                    "success": False,
                    "execution_time": time.time() - start_time,
                    "error": str(e)
                })
        
        # Calculate performance score
        score = completed_tasks / len(tasks) if tasks else 0.0
        
        performance = AgentPerformance(
            agent_id="",  # Will be set by archive
            benchmark_score=score,
            tasks_completed=completed_tasks,
            tasks_attempted=len(tasks),
            execution_time=total_time,
            timestamp=datetime.now().isoformat()
        )
        
        # Store evaluation logs
        self.evaluation_logs.extend(task_logs)
        
        # Cleanup
        if temp_agent_file.exists():
            temp_agent_file.unlink()
        
        logger.info(f"Agent evaluation complete. Score: {score:.2f}")
        return performance
    
    def run_dgm_iteration(self) -> bool:
        """Run a single DGM iteration"""

        progress = (self.iteration_count + 1) / self.max_iterations * 100
        logger.info(f"Starting DGM iteration {self.iteration_count + 1}/{self.max_iterations} ({progress:.1f}%)")
        
        # Select parent agents for modification
        parent_ids = self.agent.archive.select_parents(num_parents=1)
        
        if not parent_ids:
            logger.error("No parent agents available for modification")
            return False
        
        parent_id = parent_ids[0]
        parent_agent = self.agent.archive.get_agent(parent_id)
        
        if not parent_agent:
            logger.error(f"Parent agent {parent_id} not found")
            return False
        
        logger.info(f"Selected parent agent: {parent_id}")
        
        # Analyze recent performance logs to suggest improvements
        recent_logs = [json.dumps(log) for log in self.evaluation_logs[-10:]]
        improvement_suggestion = self.agent.analyze_performance_logs(recent_logs)
        
        logger.info(f"Improvement suggestion: {improvement_suggestion.get('improvement_suggestion', 'None')}")
        
        # Self-modify the agent
        try:
            self.agent.self_modify(improvement_suggestion)
            logger.info("Self-modification completed")
            
            # Read the modified agent code
            modified_code = Path("dgm_agent.py").read_text()
            
            # Evaluate the modified agent
            performance = self.evaluate_agent(modified_code, num_tasks=3)
            
            # Add to archive
            agent_id = self.agent.archive.add_agent(
                modified_code, 
                performance, 
                parent_id=parent_id
            )
            
            logger.info(f"New agent {agent_id} added to archive with score {performance.benchmark_score:.2f}")

            # Track improvements
            if performance.benchmark_score > self.best_score:
                self.best_score = performance.benchmark_score
                logger.info(f"🎉 NEW BEST SCORE: {self.best_score:.2f} (improvement: +{performance.benchmark_score - self.best_score:.2f})")

            self.improvement_history.append({
                "iteration": self.iteration_count + 1,
                "score": performance.benchmark_score,
                "improvement": improvement_suggestion.get('improvement_suggestion', 'Unknown'),
                "agent_id": agent_id
            })

            self.iteration_count += 1
            return True
            
        except Exception as e:
            logger.error(f"Error during self-modification: {e}")
            return False
    
    def run(self):
        """Run the complete DGM process"""
        
        logger.info("Starting Darwin Gödel Machine")
        
        # Initial evaluation
        initial_code = Path("dgm_agent.py").read_text()
        initial_performance = self.evaluate_agent(initial_code, num_tasks=3)
        
        logger.info(f"Initial agent performance: {initial_performance.benchmark_score:.2f}")
        
        # Main DGM loop
        while self.iteration_count < self.max_iterations:
            try:
                success = self.run_dgm_iteration()
                if not success:
                    logger.warning("DGM iteration failed, continuing...")
                
                # Brief pause between iterations
                time.sleep(2)
                
            except KeyboardInterrupt:
                logger.info("DGM interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in DGM iteration: {e}")
                break
        
        logger.info(f"DGM completed after {self.iteration_count} iterations")
        self.print_final_report()
    
    def print_final_report(self):
        """Print a summary report of the DGM run"""
        
        # Get all agents from archive
        import sqlite3
        conn = sqlite3.connect(self.agent.archive.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, performance_score, generation, timestamp 
            FROM agents 
            ORDER BY performance_score DESC
        """)
        
        agents = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*60)
        print("DARWIN GÖDEL MACHINE - FINAL REPORT")
        print("="*60)
        print(f"Total iterations: {self.iteration_count}")
        print(f"Total agents generated: {len(agents)}")
        print(f"Best score achieved: {self.best_score:.2f}")

        if agents:
            best_agent = agents[0]
            print(f"Best agent score: {best_agent[1]:.2f}")
            print(f"Best agent generation: {best_agent[2]}")

            print("\nTop 5 agents:")
            for i, agent in enumerate(agents[:5]):
                print(f"{i+1}. Agent {agent[0][:8]} - Score: {agent[1]:.2f} - Gen: {agent[2]}")

        print("\nImprovement History:")
        for i, improvement in enumerate(self.improvement_history[-10:]):  # Last 10 improvements
            print(f"Iter {improvement['iteration']}: Score {improvement['score']:.2f} - {improvement['improvement'][:50]}...")

        if self.best_score > 0:
            print(f"\n🎉 SUCCESS: Achieved {self.best_score:.2f} score improvement!")
        else:
            print(f"\n📈 PROGRESS: Completed {self.iteration_count} iterations, building foundation for future improvements")

        print("="*60)

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dgm.log'),
            logging.StreamHandler()
        ]
    )

    print("🧬 Starting Darwin Gödel Machine Engine")
    print("=" * 50)

    try:
        # Run DGM with extended iterations for breakthrough improvements
        engine = DGMEngine(max_iterations=25)  # Extended run for better results
        print(f"Running DGM with {engine.max_iterations} iterations...")
        print("This may take 2-3 hours. Progress will be logged continuously.")
        engine.run()
    except KeyboardInterrupt:
        print("\n⏹️  DGM stopped by user")
    except Exception as e:
        print(f"❌ DGM error: {e}")
        import traceback
        traceback.print_exc()
