#!/usr/bin/env python3
"""
Quick model test to verify Qwen3:30b-a3b is working properly
"""

import ollama
import time

def test_model_response():
    """Test basic model response"""
    print("Testing Qwen3:30b-a3b model...")
    
    try:
        start_time = time.time()
        
        response = ollama.chat(
            model='qwen3:30b-a3b',
            messages=[
                {'role': 'user', 'content': 'What is 2 + 2? Please respond briefly.'}
            ],
            options={
                'num_predict': 50,  # Very short response
                'temperature': 0.1
            }
        )
        
        end_time = time.time()
        
        print(f"✅ Model responded in {end_time - start_time:.2f} seconds")
        print(f"Response: {response['message']['content']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_coding_task():
    """Test a simple coding task"""
    print("\nTesting coding task...")
    
    try:
        start_time = time.time()
        
        response = ollama.chat(
            model='qwen3:30b-a3b',
            messages=[
                {
                    'role': 'system', 
                    'content': 'You are a coding assistant. Provide concise, working code.'
                },
                {
                    'role': 'user', 
                    'content': 'Write a Python function to add two numbers. Just the function, no explanation.'
                }
            ],
            options={
                'num_predict': 100,
                'temperature': 0.1
            }
        )
        
        end_time = time.time()
        
        print(f"✅ Coding task completed in {end_time - start_time:.2f} seconds")
        print(f"Response: {response['message']['content']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Coding task failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Quick Model Test for Darwin Gödel Machine")
    print("=" * 50)
    
    # Test basic response
    basic_ok = test_model_response()
    
    # Test coding capability
    coding_ok = test_coding_task()
    
    print("\n" + "=" * 50)
    if basic_ok and coding_ok:
        print("✅ All tests passed! Model is ready for DGM.")
        print("You can now run: python launch_dgm.py --run")
    else:
        print("❌ Some tests failed. Check Ollama and model status.")
    
    print("=" * 50)
