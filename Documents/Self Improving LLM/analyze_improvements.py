#!/usr/bin/env python3
"""
Improvement Analysis for Darwin Gödel Machine
Analyzes the archive to identify the most promising modifications
"""

import sqlite3
import json
import re
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

def analyze_archive():
    """Analyze the agent archive for patterns and improvements"""
    db_path = "agent_archive.db"
    if not Path(db_path).exists():
        print("❌ Archive database not found. Run DGM first.")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all agents with their lineage
    cursor.execute("""
        SELECT id, parent_id, performance_score, generation, timestamp, code_content
        FROM agents 
        ORDER BY timestamp
    """)
    
    agents = cursor.fetchall()
    conn.close()
    
    if not agents:
        print("❌ No agents found in archive.")
        return
    
    print("🔬 DARWIN GÖDEL MACHINE - IMPROVEMENT ANALYSIS")
    print("="*60)
    
    # Basic statistics
    total_agents = len(agents)
    scores = [agent[2] for agent in agents]
    best_score = max(scores)
    avg_score = sum(scores) / len(scores)
    generations = [agent[3] for agent in agents]
    max_generation = max(generations)
    
    print(f"📊 BASIC STATISTICS:")
    print(f"  Total agents: {total_agents}")
    print(f"  Best score: {best_score:.3f}")
    print(f"  Average score: {avg_score:.3f}")
    print(f"  Max generation: {max_generation}")
    print(f"  Score improvement: {best_score - scores[0]:.3f}")
    
    # Generation analysis
    generation_scores = defaultdict(list)
    for agent in agents:
        generation_scores[agent[3]].append(agent[2])
    
    print(f"\n📈 GENERATION ANALYSIS:")
    for gen in sorted(generation_scores.keys()):
        gen_scores = generation_scores[gen]
        avg_gen_score = sum(gen_scores) / len(gen_scores)
        best_gen_score = max(gen_scores)
        print(f"  Gen {gen}: Avg {avg_gen_score:.3f}, Best {best_gen_score:.3f}, Count {len(gen_scores)}")
    
    # Lineage analysis
    print(f"\n🌳 LINEAGE ANALYSIS:")
    lineage_map = {}
    for agent in agents:
        agent_id, parent_id, score, gen, timestamp, code = agent
        lineage_map[agent_id] = {
            'parent': parent_id,
            'score': score,
            'generation': gen,
            'children': []
        }
    
    # Build children relationships
    for agent_id, info in lineage_map.items():
        if info['parent'] and info['parent'] in lineage_map:
            lineage_map[info['parent']]['children'].append(agent_id)
    
    # Find most successful lineages
    def get_lineage_success(agent_id):
        if agent_id not in lineage_map:
            return 0
        info = lineage_map[agent_id]
        max_child_success = 0
        for child_id in info['children']:
            max_child_success = max(max_child_success, get_lineage_success(child_id))
        return max(info['score'], max_child_success)
    
    lineage_success = [(agent_id, get_lineage_success(agent_id)) 
                      for agent_id in lineage_map.keys()]
    lineage_success.sort(key=lambda x: x[1], reverse=True)
    
    print("  Most successful lineages:")
    for i, (agent_id, success) in enumerate(lineage_success[:5]):
        gen = lineage_map[agent_id]['generation']
        children_count = len(lineage_map[agent_id]['children'])
        print(f"    {i+1}. {agent_id[:8]} - Success: {success:.3f}, Gen: {gen}, Children: {children_count}")
    
    # Code analysis (if available)
    print(f"\n💻 CODE ANALYSIS:")
    if agents[0][5]:  # If code content is available
        code_changes = analyze_code_changes(agents)
        print("  Most common modifications:")
        for change, count in code_changes.most_common(5):
            print(f"    {change}: {count} times")
    else:
        print("  Code content not available in archive")
    
    # Performance trends
    print(f"\n📊 PERFORMANCE TRENDS:")
    if len(scores) > 1:
        improvements = []
        for i in range(1, len(scores)):
            if scores[i] > scores[i-1]:
                improvements.append(scores[i] - scores[i-1])
        
        if improvements:
            avg_improvement = sum(improvements) / len(improvements)
            max_improvement = max(improvements)
            print(f"  Average improvement: {avg_improvement:.3f}")
            print(f"  Maximum improvement: {max_improvement:.3f}")
            print(f"  Improvement frequency: {len(improvements)}/{len(scores)-1} iterations")
        else:
            print("  No score improvements detected")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if best_score == 0:
        print("  🔧 Focus on basic functionality:")
        print("    - Simplify task requirements")
        print("    - Improve tool integration")
        print("    - Add debugging capabilities")
    elif best_score < 0.3:
        print("  📈 Build on current progress:")
        print("    - Analyze successful partial solutions")
        print("    - Improve error handling")
        print("    - Add code validation steps")
    else:
        print("  🚀 Optimize for breakthrough:")
        print("    - Focus on high-performing lineages")
        print("    - Implement advanced techniques")
        print("    - Scale to more complex tasks")
    
    if max_generation > 10:
        print("  🧬 Consider diversity:")
        print("    - Introduce mutation mechanisms")
        print("    - Explore alternative approaches")
        print("    - Prevent premature convergence")

def analyze_code_changes(agents):
    """Analyze code changes between generations"""
    changes = Counter()
    
    for i in range(1, len(agents)):
        current_code = agents[i][5] or ""
        previous_code = agents[i-1][5] or ""
        
        # Simple diff analysis
        current_lines = set(current_code.split('\n'))
        previous_lines = set(previous_code.split('\n'))
        
        added_lines = current_lines - previous_lines
        removed_lines = previous_lines - current_lines
        
        # Categorize changes
        for line in added_lines:
            line = line.strip()
            if not line:
                continue
            
            if 'def ' in line:
                changes['Added function'] += 1
            elif 'class ' in line:
                changes['Added class'] += 1
            elif 'import ' in line:
                changes['Added import'] += 1
            elif 'if ' in line:
                changes['Added conditional'] += 1
            elif 'try:' in line or 'except' in line:
                changes['Added error handling'] += 1
            elif 'logger' in line or 'print' in line:
                changes['Added logging'] += 1
            else:
                changes['Other addition'] += 1
        
        for line in removed_lines:
            line = line.strip()
            if line:
                changes['Removed code'] += 1
    
    return changes

def analyze_logs():
    """Analyze log files for improvement patterns"""
    log_file = "dgm.log"
    if not Path(log_file).exists():
        print("❌ Log file not found.")
        return
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Extract improvement suggestions
    improvement_pattern = r"Improvement suggestion: (.+)"
    improvements = re.findall(improvement_pattern, content)
    
    print(f"\n💭 IMPROVEMENT SUGGESTIONS ANALYSIS:")
    print(f"  Total suggestions: {len(improvements)}")
    
    if improvements:
        # Categorize suggestions
        categories = Counter()
        for suggestion in improvements:
            suggestion_lower = suggestion.lower()
            if 'validation' in suggestion_lower or 'check' in suggestion_lower:
                categories['Validation/Checking'] += 1
            elif 'workflow' in suggestion_lower or 'process' in suggestion_lower:
                categories['Workflow Improvement'] += 1
            elif 'structure' in suggestion_lower or 'organization' in suggestion_lower:
                categories['Code Structure'] += 1
            elif 'test' in suggestion_lower:
                categories['Testing'] += 1
            elif 'error' in suggestion_lower or 'exception' in suggestion_lower:
                categories['Error Handling'] += 1
            else:
                categories['Other'] += 1
        
        print("  Suggestion categories:")
        for category, count in categories.most_common():
            print(f"    {category}: {count}")
        
        print("\n  Recent suggestions:")
        for i, suggestion in enumerate(improvements[-5:]):
            print(f"    {len(improvements)-4+i}. {suggestion[:60]}...")

def main():
    """Main analysis function"""
    analyze_archive()
    analyze_logs()
    
    print(f"\n🎯 NEXT STEPS:")
    print("  1. Run longer experiments (50+ iterations)")
    print("  2. Analyze successful modifications in detail")
    print("  3. Implement targeted improvements based on patterns")
    print("  4. Consider ensemble approaches with multiple agents")
    print("="*60)

if __name__ == "__main__":
    main()
