# Fast Darwin Gödel Machine Guide

## 🚀 Quick Start

### Issues Fixed:
1. **Agent ID Problem**: Fixed unique agent ID generation per iteration
2. **Performance**: Reduced timeouts from 4min to 2min per task, 8→5 iterations per task
3. **Persistence**: Added checkpoint system to save/restore progress
4. **Monitoring**: Added real-time progress monitoring

### Estimated Performance:
- **Old system**: ~10 hours for 25 iterations
- **New system**: ~2.5 hours for 25 iterations (4x faster!)

## 📋 Commands

### 1. Start Fast DGM
```bash
# Start fresh run
python run_dgm_fast.py

# Start with custom iterations
python run_dgm_fast.py --iterations 50

# Resume from checkpoint
python run_dgm_fast.py --resume

# Reset and start fresh
python run_dgm_fast.py --reset
```

### 2. Monitor Progress (in another terminal)
```bash
# One-time status check
python monitor_dgm.py

# Continuous monitoring (refreshes every 30s)
python monitor_dgm.py --continuous

# Custom refresh interval
python monitor_dgm.py --continuous --interval 10
```

### 3. Stop and Resume
- **Stop**: Press `Ctrl+C` in the DGM terminal (saves checkpoint automatically)
- **Resume**: Run `python run_dgm_fast.py` again (automatically resumes from checkpoint)

## 🔧 Configuration Options

### run_dgm_fast.py Options:
```bash
--iterations, -i    Number of iterations (default: 25)
--checkpoint, -c    Checkpoint file path (default: dgm_checkpoint.json)
--resume, -r        Resume from existing checkpoint
--reset             Delete checkpoint and start fresh
```

### monitor_dgm.py Options:
```bash
--checkpoint, -c    Checkpoint file path
--database, -d      Agent database file path
--continuous        Monitor continuously
--interval, -i      Refresh interval in seconds (default: 30)
```

## 📊 What You'll See

### DGM Runner Output:
```
🚀 Fast Darwin Gödel Machine Runner
==================================================
📊 Configuration:
   Max iterations: 25
   Checkpoint file: dgm_checkpoint.json
   Starting from iteration: 0
   Current best score: 0.00
🆕 Starting fresh run...
📈 Initial performance: 0.00
⏱️  Estimated time: 150 minutes
🔄 Starting DGM iterations...
   Press Ctrl+C to stop gracefully and save checkpoint

✅ Iteration 1/25 (4.0%) - 6.2s - Best: 0.00
✅ Iteration 2/25 (8.0%) - 5.8s - Best: 0.00
...
```

### Monitor Output:
```
🧬 DARWIN GÖDEL MACHINE - LIVE MONITOR
============================================================
📊 Progress: 12/25 (48.0%)
🏆 Best Score: 0.33
⏰ Last Update: 2025-06-06T13:16:15.468000

📈 Recent Improvements:
   Iter 12: 0.33 - Implement a file validation check before testing...
   Iter 13: 0.33 - Implement a file verification step after creation...
   Iter 14: 0.33 - Implement a dedicated file creation tool with...

🤖 Agent Archive:
   Total Agents: 14
   Best Score: 0.33

📊 Score Distribution:
   0.33: 4 agents
   0.00: 10 agents

🕒 Recent Agents:
   a4faa648 - Score: 0.33 - Gen: 12
   b7c2d891 - Score: 0.33 - Gen: 13
   ...
============================================================
```

## 🛠️ Troubleshooting

### If DGM Stops Unexpectedly:
1. Check `dgm_fast.log` for errors
2. Resume with: `python run_dgm_fast.py`
3. Monitor progress: `python monitor_dgm.py`

### If Performance is Still Slow:
1. Reduce iterations: `python run_dgm_fast.py --iterations 10`
2. Check system resources (CPU, memory)
3. Ensure Ollama is running efficiently

### If Agent IDs are Still the Same:
1. Check that the modified `dgm_agent.py` is being used
2. Restart the DGM process
3. The new system should generate unique IDs like: `b7c2d891`, `f3e8a456`, etc.

## 📈 Expected Results

Based on the previous run, you should see:
- **Iterations 1-11**: Exploration phase (0.00 scores)
- **Iteration 12+**: Breakthrough phase (0.33+ scores)
- **Unique agent IDs**: Each iteration creates a new unique agent
- **Faster execution**: ~6 minutes per iteration instead of 24 minutes

## 🎯 Success Indicators

1. **Unique Agent IDs**: Each iteration shows different agent ID (not all `a4faa648379ee638`)
2. **Faster Iterations**: Each iteration completes in ~6 minutes instead of 24 minutes
3. **Checkpoint Saves**: "✅ Checkpoint saved" appears after each iteration
4. **Progress Resumption**: Can stop/start without losing progress
5. **Score Improvements**: Should see breakthrough around iteration 12

## 🔄 Workflow Example

```bash
# Terminal 1: Start DGM
python run_dgm_fast.py --iterations 25

# Terminal 2: Monitor progress
python monitor_dgm.py --continuous

# If you need to stop:
# Press Ctrl+C in Terminal 1
# DGM saves checkpoint automatically

# To resume later:
python run_dgm_fast.py
# Automatically resumes from where it left off
```

This optimized system should give you the same breakthrough results but 4x faster with full persistence support!
