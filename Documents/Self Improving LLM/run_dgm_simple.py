#!/usr/bin/env python3
"""
Simple DGM runner for debugging
"""

import logging
import sys
from dgm_engine import DGMEngine

# Setup logging to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def main():
    try:
        print("🧬 Starting Darwin Gödel Machine (Simple Version)")
        print("=" * 50)
        
        # Create engine with fewer iterations for testing
        engine = DGMEngine(max_iterations=5)
        print(f"Created engine with {engine.max_iterations} iterations")
        
        # Run initial evaluation only
        print("Running initial evaluation...")
        initial_code = """
# Initial agent code placeholder
def placeholder():
    pass
"""
        
        performance = engine.evaluate_agent(initial_code, num_tasks=2)
        print(f"Initial evaluation complete. Score: {performance.benchmark_score:.2f}")
        
        # Try one iteration
        print("Attempting one DGM iteration...")
        success = engine.run_dgm_iteration()
        print(f"Iteration result: {'Success' if success else 'Failed'}")
        
        print("✅ Simple test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
