#!/usr/bin/env python3
"""
DGM Progress Monitor
Real-time monitoring of Darwin Gödel Machine progress
"""

import json
import time
import sqlite3
from pathlib import Path
from datetime import datetime
import argparse

class DGMMonitor:
    """Monitor DGM progress in real-time"""
    
    def __init__(self, checkpoint_file: str = "dgm_checkpoint.json", 
                 db_file: str = "agent_archive.db"):
        self.checkpoint_file = checkpoint_file
        self.db_file = db_file
        self.last_iteration = -1
    
    def load_checkpoint(self):
        """Load current checkpoint data"""
        try:
            if not Path(self.checkpoint_file).exists():
                return None
            
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading checkpoint: {e}")
            return None
    
    def get_agent_stats(self):
        """Get agent statistics from database"""
        try:
            if not Path(self.db_file).exists():
                return None
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Get total agents
            cursor.execute("SELECT COUNT(*) FROM agents")
            total_agents = cursor.fetchone()[0]
            
            # Get best score
            cursor.execute("SELECT MAX(performance_score) FROM agents")
            best_score = cursor.fetchone()[0] or 0.0
            
            # Get recent agents
            cursor.execute("""
                SELECT id, performance_score, generation, timestamp 
                FROM agents 
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            recent_agents = cursor.fetchall()
            
            # Get score distribution
            cursor.execute("""
                SELECT performance_score, COUNT(*) 
                FROM agents 
                GROUP BY performance_score 
                ORDER BY performance_score DESC
            """)
            score_distribution = cursor.fetchall()
            
            conn.close()
            
            return {
                "total_agents": total_agents,
                "best_score": best_score,
                "recent_agents": recent_agents,
                "score_distribution": score_distribution
            }
        except Exception as e:
            print(f"Error getting agent stats: {e}")
            return None
    
    def print_status(self, checkpoint_data, agent_stats):
        """Print current status"""
        print("\n" + "="*60)
        print("🧬 DARWIN GÖDEL MACHINE - LIVE MONITOR")
        print("="*60)
        
        if checkpoint_data:
            iteration = checkpoint_data.get("iteration_count", 0)
            max_iterations = checkpoint_data.get("max_iterations", 25)
            best_score = checkpoint_data.get("best_score", 0.0)
            progress = (iteration / max_iterations) * 100
            
            print(f"📊 Progress: {iteration}/{max_iterations} ({progress:.1f}%)")
            print(f"🏆 Best Score: {best_score:.2f}")
            print(f"⏰ Last Update: {checkpoint_data.get('timestamp', 'Unknown')}")
            
            # Show recent improvements
            history = checkpoint_data.get("improvement_history", [])
            if history:
                print(f"\n📈 Recent Improvements:")
                for improvement in history[-3:]:
                    iter_num = improvement.get("iteration", "?")
                    score = improvement.get("score", 0.0)
                    suggestion = improvement.get("improvement", "Unknown")[:50]
                    print(f"   Iter {iter_num}: {score:.2f} - {suggestion}...")
        
        if agent_stats:
            print(f"\n🤖 Agent Archive:")
            print(f"   Total Agents: {agent_stats['total_agents']}")
            print(f"   Best Score: {agent_stats['best_score']:.2f}")
            
            print(f"\n📊 Score Distribution:")
            for score, count in agent_stats['score_distribution'][:5]:
                print(f"   {score:.2f}: {count} agents")
            
            print(f"\n🕒 Recent Agents:")
            for agent_id, score, generation, timestamp in agent_stats['recent_agents']:
                short_id = agent_id[:8]
                print(f"   {short_id} - Score: {score:.2f} - Gen: {generation}")
        
        print("="*60)
    
    def monitor_continuous(self, refresh_interval: int = 30):
        """Monitor continuously with refresh"""
        print("🔍 Starting continuous monitoring...")
        print(f"📱 Refresh interval: {refresh_interval} seconds")
        print("⏹️  Press Ctrl+C to stop")
        
        try:
            while True:
                checkpoint_data = self.load_checkpoint()
                agent_stats = self.get_agent_stats()
                
                # Clear screen (works on most terminals)
                print("\033[2J\033[H", end="")
                
                self.print_status(checkpoint_data, agent_stats)
                
                # Check if new iteration
                if checkpoint_data:
                    current_iteration = checkpoint_data.get("iteration_count", 0)
                    if current_iteration > self.last_iteration:
                        print(f"🆕 New iteration detected: {current_iteration}")
                        self.last_iteration = current_iteration
                
                print(f"\n⏱️  Next refresh in {refresh_interval} seconds...")
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n⏹️  Monitoring stopped")
    
    def monitor_once(self):
        """Monitor once and exit"""
        checkpoint_data = self.load_checkpoint()
        agent_stats = self.get_agent_stats()
        self.print_status(checkpoint_data, agent_stats)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="DGM Progress Monitor")
    parser.add_argument("--checkpoint", "-c", type=str, default="dgm_checkpoint.json",
                       help="Checkpoint file path")
    parser.add_argument("--database", "-d", type=str, default="agent_archive.db",
                       help="Agent database file path")
    parser.add_argument("--continuous", action="store_true",
                       help="Monitor continuously")
    parser.add_argument("--interval", "-i", type=int, default=30,
                       help="Refresh interval for continuous monitoring (seconds)")
    
    args = parser.parse_args()
    
    monitor = DGMMonitor(
        checkpoint_file=args.checkpoint,
        db_file=args.database
    )
    
    if args.continuous:
        monitor.monitor_continuous(args.interval)
    else:
        monitor.monitor_once()

if __name__ == "__main__":
    main()
