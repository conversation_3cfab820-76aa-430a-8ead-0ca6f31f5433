#!/usr/bin/env python3
"""
Progress Monitor for Darwin Gödel Machine
Real-time monitoring of the DGM progress during long runs
"""

import sqlite3
import time
import os
from datetime import datetime
from pathlib import Path

def get_archive_stats():
    """Get current statistics from the archive"""
    db_path = "agent_archive.db"
    if not Path(db_path).exists():
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get total agents
        cursor.execute("SELECT COUNT(*) FROM agents")
        total_agents = cursor.fetchone()[0]
        
        # Get best score
        cursor.execute("SELECT MAX(performance_score) FROM agents")
        best_score = cursor.fetchone()[0] or 0.0
        
        # Get latest generation
        cursor.execute("SELECT MAX(generation) FROM agents")
        max_generation = cursor.fetchone()[0] or 0
        
        # Get recent agents (last 5)
        cursor.execute("""
            SELECT id, performance_score, generation, timestamp 
            FROM agents 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        recent_agents = cursor.fetchall()
        
        # Get improvement trend (last 10 agents)
        cursor.execute("""
            SELECT performance_score, generation 
            FROM agents 
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        trend_data = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_agents': total_agents,
            'best_score': best_score,
            'max_generation': max_generation,
            'recent_agents': recent_agents,
            'trend_data': trend_data
        }
        
    except Exception as e:
        print(f"Error reading archive: {e}")
        return None

def get_log_stats():
    """Get statistics from log files"""
    log_file = "dgm.log"
    if not Path(log_file).exists():
        return None
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # Count iterations
        iteration_lines = [line for line in lines if "Starting DGM iteration" in line]
        current_iteration = len(iteration_lines)
        
        # Find last improvement suggestion
        improvement_lines = [line for line in lines if "Improvement suggestion:" in line]
        last_improvement = improvement_lines[-1].split("Improvement suggestion:")[-1].strip() if improvement_lines else "None"
        
        # Count task completions
        task_lines = [line for line in lines if "completed successfully" in line]
        successful_tasks = len(task_lines)
        
        return {
            'current_iteration': current_iteration,
            'last_improvement': last_improvement,
            'successful_tasks': successful_tasks,
            'total_log_lines': len(lines)
        }
        
    except Exception as e:
        print(f"Error reading logs: {e}")
        return None

def print_progress_report():
    """Print a comprehensive progress report"""
    print("\n" + "="*60)
    print("DARWIN GÖDEL MACHINE - PROGRESS MONITOR")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Archive statistics
    archive_stats = get_archive_stats()
    if archive_stats:
        print(f"\n📊 ARCHIVE STATISTICS:")
        print(f"  Total agents: {archive_stats['total_agents']}")
        print(f"  Best score: {archive_stats['best_score']:.3f}")
        print(f"  Max generation: {archive_stats['max_generation']}")
        
        if archive_stats['recent_agents']:
            print(f"\n🔄 RECENT AGENTS:")
            for i, agent in enumerate(archive_stats['recent_agents']):
                agent_id, score, gen, timestamp = agent
                print(f"  {i+1}. {agent_id[:8]} - Score: {score:.3f} - Gen: {gen}")
        
        if archive_stats['trend_data']:
            scores = [agent[0] for agent in archive_stats['trend_data']]
            if len(scores) > 1:
                trend = "📈 Improving" if scores[0] > scores[-1] else "📉 Declining" if scores[0] < scores[-1] else "➡️ Stable"
                print(f"\n📈 TREND: {trend}")
    else:
        print("\n📊 ARCHIVE: Not yet created")
    
    # Log statistics
    log_stats = get_log_stats()
    if log_stats:
        print(f"\n🔄 EXECUTION PROGRESS:")
        print(f"  Current iteration: {log_stats['current_iteration']}")
        print(f"  Successful tasks: {log_stats['successful_tasks']}")
        print(f"  Log lines: {log_stats['total_log_lines']}")
        
        if log_stats['last_improvement']:
            print(f"\n💡 LAST IMPROVEMENT:")
            print(f"  {log_stats['last_improvement'][:80]}...")
    else:
        print("\n🔄 EXECUTION: Not yet started")
    
    # Process status
    if Path("dgm.log").exists():
        log_age = time.time() - Path("dgm.log").stat().st_mtime
        if log_age < 300:  # Less than 5 minutes old
            print(f"\n✅ STATUS: Active (last update {log_age:.0f}s ago)")
        else:
            print(f"\n⚠️ STATUS: Possibly stalled (last update {log_age:.0f}s ago)")
    else:
        print(f"\n❌ STATUS: Not running")
    
    print("="*60)

def monitor_continuously(interval=60):
    """Monitor progress continuously"""
    print("🔍 Starting continuous monitoring...")
    print(f"Updating every {interval} seconds. Press Ctrl+C to stop.")
    
    try:
        while True:
            print_progress_report()
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")

def main():
    """Main monitoring function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor Darwin Gödel Machine progress")
    parser.add_argument('--continuous', '-c', action='store_true', 
                       help='Monitor continuously')
    parser.add_argument('--interval', '-i', type=int, default=60,
                       help='Update interval in seconds (default: 60)')
    
    args = parser.parse_args()
    
    if args.continuous:
        monitor_continuously(args.interval)
    else:
        print_progress_report()

if __name__ == "__main__":
    main()
