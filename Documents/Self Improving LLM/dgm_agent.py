#!/usr/bin/env python3
"""
Darwin Gödel Machine Implementation for Qwen3:30b-a3b
Self-improving coding agent based on the DGM research paper
"""

import os
import json
import time
import logging
import subprocess
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import sqlite3
import hashlib
import ollama

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class AgentPerformance:
    """Performance metrics for an agent"""
    agent_id: str
    benchmark_score: float
    tasks_completed: int
    tasks_attempted: int
    execution_time: float
    timestamp: str
    parent_id: Optional[str] = None

@dataclass
class SolutionAttempt:
    """Individual solution attempt with detailed metrics"""
    patch: str
    test_output: str
    test_success: bool
    test_stats: Dict[str, Any]
    error_messages: List[str]
    execution_time: float
    attempt_number: int

class ToolRegistry:
    """Registry for agent tools"""
    
    def __init__(self):
        self.tools = {}
    
    def register_tool(self, name: str, tool_function, tool_info):
        """Register a new tool"""
        self.tools[name] = {
            'function': tool_function,
            'info': tool_info
        }
    
    def get_tool(self, name: str):
        """Get a tool by name"""
        return self.tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all available tools"""
        return list(self.tools.keys())

class BashTool:
    """Basic bash execution tool"""
    
    @staticmethod
    def tool_info():
        return {
            "name": "bash",
            "description": "Execute bash commands in a sandboxed environment",
            "input_schema": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The bash command to execute"
                    }
                },
                "required": ["command"]
            }
        }
    
    @staticmethod
    def tool_function(command: str) -> str:
        """Execute a bash command and return the output"""
        try:
            # Add safety restrictions
            forbidden_commands = ['rm -rf', 'sudo', 'chmod 777', 'mkfs', 'dd if=']
            if any(cmd in command.lower() for cmd in forbidden_commands):
                return "Error: Command contains forbidden operations"
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30,  # 30 second timeout
                cwd="/tmp"  # Restrict to tmp directory
            )
            
            output = f"Exit code: {result.returncode}\n"
            if result.stdout:
                output += f"STDOUT:\n{result.stdout}\n"
            if result.stderr:
                output += f"STDERR:\n{result.stderr}\n"
            
            return output
            
        except subprocess.TimeoutExpired:
            return "Error: Command timed out after 30 seconds"
        except Exception as e:
            return f"Error executing command: {str(e)}"

class EditTool:
    """File editing tool with safety restrictions"""
    
    @staticmethod
    def tool_info():
        return {
            "name": "edit",
            "description": "View and edit files safely",
            "input_schema": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "enum": ["view", "create", "str_replace"],
                        "description": "The command to run: view, create, or str_replace"
                    },
                    "path": {
                        "type": "string",
                        "description": "Path to the file"
                    },
                    "file_text": {
                        "type": "string",
                        "description": "Content for create command"
                    },
                    "old_str": {
                        "type": "string",
                        "description": "Text to find and replace"
                    },
                    "new_str": {
                        "type": "string",
                        "description": "Replacement text"
                    }
                },
                "required": ["command", "path"]
            }
        }
    
    @staticmethod
    def tool_function(command: str, path: str, file_text: str = None, 
                     old_str: str = None, new_str: str = None) -> str:
        """Execute file operations safely"""
        try:
            # Security: restrict to current working directory and subdirectories
            safe_path = Path(path).resolve()
            cwd = Path.cwd().resolve()
            
            if not str(safe_path).startswith(str(cwd)):
                return "Error: Access denied - path outside working directory"
            
            if command == "view":
                if safe_path.exists() and safe_path.is_file():
                    with open(safe_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    return f"File content of {path}:\n{content}"
                else:
                    return f"Error: File {path} does not exist"
            
            elif command == "create":
                if file_text is None:
                    return "Error: file_text required for create command"
                
                safe_path.parent.mkdir(parents=True, exist_ok=True)
                with open(safe_path, 'w', encoding='utf-8') as f:
                    f.write(file_text)
                return f"File created successfully at {path}"
            
            elif command == "str_replace":
                if old_str is None or new_str is None:
                    return "Error: old_str and new_str required for str_replace command"
                
                if not safe_path.exists():
                    return f"Error: File {path} does not exist"
                
                with open(safe_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if old_str not in content:
                    return f"Error: Text '{old_str}' not found in file"
                
                if content.count(old_str) > 1:
                    return f"Error: Text '{old_str}' appears multiple times in file"
                
                new_content = content.replace(old_str, new_str)
                with open(safe_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                return f"Successfully replaced text in {path}"
            
            else:
                return f"Error: Unknown command {command}"
                
        except Exception as e:
            return f"Error: {str(e)}"

class ArchiveManager:
    """Manages the archive of discovered agents"""
    
    def __init__(self, db_path: str = "agent_archive.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agents (
                id TEXT PRIMARY KEY,
                parent_id TEXT,
                code_hash TEXT,
                code_content TEXT,
                performance_score REAL,
                tasks_completed INTEGER,
                tasks_attempted INTEGER,
                execution_time REAL,
                timestamp TEXT,
                generation INTEGER,
                is_valid BOOLEAN
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT,
                modification_type TEXT,
                description TEXT,
                diff TEXT,
                timestamp TEXT,
                FOREIGN KEY (agent_id) REFERENCES agents (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_agent(self, agent_code: str, performance: AgentPerformance, 
                  parent_id: Optional[str] = None) -> str:
        """Add a new agent to the archive"""
        agent_id = hashlib.sha256(agent_code.encode()).hexdigest()[:16]
        code_hash = hashlib.sha256(agent_code.encode()).hexdigest()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Determine generation
        generation = 0
        if parent_id:
            cursor.execute("SELECT generation FROM agents WHERE id = ?", (parent_id,))
            result = cursor.fetchone()
            if result:
                generation = result[0] + 1
        
        cursor.execute('''
            INSERT OR REPLACE INTO agents 
            (id, parent_id, code_hash, code_content, performance_score, 
             tasks_completed, tasks_attempted, execution_time, timestamp, 
             generation, is_valid)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            agent_id, parent_id, code_hash, agent_code, performance.benchmark_score,
            performance.tasks_completed, performance.tasks_attempted, 
            performance.execution_time, performance.timestamp, generation, True
        ))
        
        conn.commit()
        conn.close()
        
        return agent_id
    
    def get_agent(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve an agent by ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM agents WHERE id = ?", (agent_id,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            columns = [desc[0] for desc in cursor.description]
            return dict(zip(columns, result))
        return None
    
    def select_parents(self, num_parents: int = 2) -> List[str]:
        """Select parent agents for modification"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Select based on performance score and diversity
        cursor.execute('''
            SELECT id, performance_score FROM agents 
            WHERE is_valid = 1 
            ORDER BY performance_score DESC, RANDOM()
            LIMIT ?
        ''', (num_parents * 2,))
        
        candidates = cursor.fetchall()
        conn.close()
        
        # Simple selection: top performers with some randomness
        selected = []
        for agent_id, score in candidates[:num_parents]:
            selected.append(agent_id)
        
        return selected

class QwenCodingAgent:
    """Main coding agent powered by Qwen3:30b-a3b"""
    
    def __init__(self, model_name: str = "qwen3:30b-a3b"):
        self.model_name = model_name
        self.tools = ToolRegistry()
        self.archive = ArchiveManager()
        self.setup_tools()
        
        # Verify Ollama connection
        try:
            ollama.list()
            logger.info(f"Connected to Ollama, using model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
            raise
    
    def setup_tools(self):
        """Initialize the tool registry"""
        self.tools.register_tool("bash", BashTool.tool_function, BashTool.tool_info())
        self.tools.register_tool("edit", EditTool.tool_function, EditTool.tool_info())
    
    def chat_with_model(self, prompt: str, system_prompt: str = None) -> str:
        """Chat with the Qwen model"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = ollama.chat(
                model=self.model_name,
                messages=messages,
                options={
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 4096
                }
            )
            
            return response['message']['content']
            
        except Exception as e:
            logger.error(f"Error communicating with model: {e}")
            return f"Error: {str(e)}"

    def execute_tool(self, tool_name: str, **kwargs) -> str:
        """Execute a tool with given parameters"""
        tool = self.tools.get_tool(tool_name)
        if not tool:
            return f"Error: Tool '{tool_name}' not found"

        try:
            return tool['function'](**kwargs)
        except Exception as e:
            return f"Error executing tool '{tool_name}': {str(e)}"

    def solve_coding_task(self, task_description: str, repo_path: Optional[str] = None) -> Tuple[str, bool]:
        """Solve a coding task and return the solution and success status"""

        system_prompt = """You are an expert coding agent. You have access to tools for executing bash commands and editing files.

Available tools:
- bash: Execute bash commands (use for running tests, installing packages, etc.)
- edit: View, create, and modify files using str_replace for precise edits

When you need to use a tool, format your response as:
TOOL_USE: tool_name
PARAMETERS: {"param1": "value1", "param2": "value2"}

Always analyze the problem carefully, explore the codebase, understand the requirements, and implement a complete solution.
Focus on writing clean, working code that passes all tests."""

        prompt = f"""Task: {task_description}

Repository path: {repo_path or 'current directory'}

Please solve this coding task step by step:
1. Understand the problem requirements
2. Explore the existing codebase if applicable
3. Implement the solution
4. Test your implementation
5. Provide a summary of changes made

Start by exploring the repository structure and understanding what needs to be done."""

        conversation_history = []
        max_iterations = 20
        iteration = 0
        response = ""  # Initialize response variable

        while iteration < max_iterations:
            iteration += 1

            # Get response from model
            full_prompt = prompt
            if conversation_history:
                full_prompt += "\n\nPrevious actions:\n" + "\n".join(conversation_history)

            response = self.chat_with_model(full_prompt, system_prompt)
            conversation_history.append(f"Assistant: {response}")

            # Check if response contains tool usage
            if "TOOL_USE:" in response and "PARAMETERS:" in response:
                lines = response.split('\n')
                tool_name = None
                parameters = None

                for i, line in enumerate(lines):
                    if line.startswith("TOOL_USE:"):
                        tool_name = line.split("TOOL_USE:")[1].strip()
                    elif line.startswith("PARAMETERS:"):
                        try:
                            param_str = line.split("PARAMETERS:")[1].strip()
                            parameters = json.loads(param_str)
                        except json.JSONDecodeError:
                            # Try to extract parameters from multiple lines
                            param_lines = []
                            for j in range(i+1, len(lines)):
                                if lines[j].strip().startswith('{'):
                                    param_lines.append(lines[j])
                                    break
                            if param_lines:
                                try:
                                    parameters = json.loads(param_lines[0])
                                except json.JSONDecodeError:
                                    parameters = None

                if tool_name and parameters:
                    # Execute the tool
                    tool_result = self.execute_tool(tool_name, **parameters)
                    conversation_history.append(f"Tool {tool_name} result: {tool_result}")

                    # Continue the conversation with tool result
                    prompt = f"Tool execution result:\n{tool_result}\n\nPlease continue with the next step or provide your final solution if complete."
                else:
                    # No valid tool usage found, assume task is complete
                    break
            else:
                # No tool usage, assume task is complete
                break

        # Determine if task was completed successfully
        success = "error" not in response.lower() and "failed" not in response.lower()

        return response, success

    def analyze_performance_logs(self, logs: List[str]) -> Dict[str, Any]:
        """Analyze performance logs to suggest improvements"""

        system_prompt = """You are an expert at analyzing coding agent performance and suggesting improvements.

Your task is to analyze the provided logs and suggest ONE specific improvement that would enhance the agent's coding capabilities.

Focus on:
- Tool limitations or missing functionality
- Workflow inefficiencies
- Common failure patterns
- Opportunities for better code understanding or generation

Provide your response in JSON format:
{
    "analysis": "Brief analysis of the main issues",
    "improvement_suggestion": "Specific improvement to implement",
    "implementation_details": "How to implement this improvement",
    "expected_benefit": "What improvement this should provide"
}"""

        logs_text = "\n".join(logs)
        prompt = f"""Analyze these performance logs from a coding agent:

{logs_text}

Based on these logs, what is the most impactful improvement that could be made to enhance the agent's coding capabilities?"""

        response = self.chat_with_model(prompt, system_prompt)

        try:
            # Extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
        except:
            pass

        # Fallback if JSON parsing fails
        return {
            "analysis": "Could not parse analysis",
            "improvement_suggestion": response,
            "implementation_details": "Manual implementation required",
            "expected_benefit": "Unknown"
        }

    def self_modify(self, improvement_suggestion: Dict[str, Any]) -> str:
        """Modify the agent's own code based on improvement suggestion"""

        system_prompt = """You are a coding agent that can modify its own code to improve its capabilities.

You have access to your current implementation and need to make specific improvements.

Available tools:
- edit: View and modify files using str_replace for precise edits
- bash: Execute commands to test changes

When modifying code:
1. First understand the current implementation
2. Make targeted improvements based on the suggestion
3. Test the changes to ensure they work
4. Provide a summary of what was changed

Be careful to maintain existing functionality while adding improvements."""

        current_code = Path(__file__).read_text()

        prompt = f"""Current agent implementation:

{current_code}

Improvement suggestion:
{json.dumps(improvement_suggestion, indent=2)}

Please implement this improvement by modifying the agent's code. Start by examining the current code structure and then make the necessary changes."""

        response = self.chat_with_model(prompt, system_prompt)

        return response
